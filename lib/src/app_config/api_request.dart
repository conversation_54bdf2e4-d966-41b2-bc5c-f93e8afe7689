import 'package:shared_preferences/shared_preferences.dart';

import '/src/DAPackagesRef.dart';
import '/src/app_config/app_config.dart';

class ApiRequest {
  static ApiRequest _instancia = new ApiRequest._internal();

  ApiRequest._internal();
  factory ApiRequest() {
    return _instancia;
  }

  // Valida sesión
  static Future<dynamic> execAPI(String uri, dynamic body,
      {bool? update}) async {
    try {
      if ((update ?? false) == true) {
        return DAMainLoadingProvider.updateAPI(uri, body);
      }

      return DAMainLoadingProvider.execAPI(uri, body);
    } catch (e) {
      if (e.toString() == "SESIÓN EXPIRADA") {
        AppConfig.logOut();
        return;
      }
      throw e.toString();
    }
  }

  // Datos Perfil
  static DARequestModel perfil() {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spPGM_ObtenerDetallesCliente",
      bodyReq: {
        "Cliente": "{{Usuario}}",
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  // Cambiar Contraseña
  static DARequestModel cambiarContrasena(String contrasena) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spPGM_CambiarContrasenaCliente",
      bodyReq: {
        "Cliente": "{{Usuario}}",
        "Contrasena": contrasena,
      },
    );

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  // Obtencion de Desarrollos
  static DARequestModel getDesarrollos() {
    Map<String, dynamic> _preBodyReq = {
      "Cliente": "{{Usuario}}",
    };
    String uri = "/PROC/spPGM_DesarrollosCte";
    DARequestModel _req = new DARequestModel(uriReq: uri, bodyReq: _preBodyReq);

    _req.bodyReq = DAController.sessionRequest(_preBodyReq);
    return _req;
  }

  // Obtencion de Terrenos de los Desarrollos
  static DARequestModel getTerrenosDesarrollos(String contrato) {
    Map<String, dynamic> _preBodyReq = {
      "Contrato": contrato,
    };
    String uri = "/PROC/spPGM_TerrenosDesarrollo";
    DARequestModel _req = new DARequestModel(uriReq: uri, bodyReq: _preBodyReq);

    //_req.uriReq = DAController.sessionUrl(uri);
    return _req;
  }

  // Obtencion de FacturarA
  static DARequestModel getFacturarA() {
    Map<String, dynamic> _preBodyReq = {
      "Cliente": "{{Usuario}}",
    };
    String uri = "/PROC/spPGM_FacturarA";
    DARequestModel _req = new DARequestModel(uriReq: uri, bodyReq: _preBodyReq);

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  // Obtencion de Documentos
  static DARequestModel getDocumentos({
    required String empresa,
    required String cliente,
    required String contrato,
  }) {
    Map<String, dynamic> _preBodyReq = {
      "Empresa": empresa,
      "Cliente": cliente,
      "IDContraro": contrato,
    };
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/sp_WEB_MovsPorPagar",
      bodyReq: _preBodyReq,
    );
    return _req;
  }

  // Obtencion de Estados de Cuenta
  static DARequestModel getEstadosDeCuenta({
    required String empresa,
    required String cliente,
    String? inmueble,
    String? local,
  }) {
    Map<String, dynamic> _preBodyReq = {
      "Empresa": empresa,
      "Cliente": cliente,
      "App": 1
    };
    if (inmueble != null) {
      _preBodyReq['Inmueble'] = inmueble;
    }
    if (local != null) {
      _preBodyReq['Local'] = local;
    }
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spPANGEAEdoCtaWEB",
      bodyReq: _preBodyReq,
    );
    return _req;
  }

  // Datos de Estado de Cuenta
  static DARequestModel getDatosEstadoCuenta({
    required String empresa,
    required String inmueble,
    required String local,
  }) {
    DARequestModel _req = new DARequestModel(
      uriReq: "/PROC/spPGM_ObtenerDatosEdC",
      bodyReq: {
        "Param_Empresa": empresa,
        "Param_Inmueble": inmueble,
        "Param_Local": local,
      },
    );
    return _req;
  }

  // Datos del Cliente de Estado de Cuenta
  static DARequestModel getDatosClienteEstadoCuenta({
    required String empresa,
  }) {
    String uri = "/PROC/spPGM_ObtenerDatosClientesEdC";

    Map<String, dynamic> _preBodyReq = {
      "Cliente": "{{Usuario}}",
      "Empresa": empresa,
    };

    DARequestModel _req = new DARequestModel(uriReq: uri, bodyReq: _preBodyReq);

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  // Token FCM
  static Future<DARequestModel> get guardarToken async {
    String uriRoute = '/PROC/spGuardarToken';

    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('fcmToken');
    DARequestModel _req = new DARequestModel(uriReq: uriRoute, bodyReq: {
      "Token": token == null ? '' : token,
      "Usuario": '{{Usuario}}',
      "Aplicacion": "Pagos",
      "Logout": 0
    });

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static Future<DARequestModel> get desactivarToken async {
    String uriRoute = '/PROC/spGuardarToken';

    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString('fcmToken');
    DARequestModel _req = new DARequestModel(uriReq: uriRoute, bodyReq: {
      "Token": token == null ? '' : token,
      "Usuario": '{{Usuario}}',
      "Aplicacion": "Pagos",
      "Logout": 1
    });

    _req.bodyReq = DAController.sessionRequest(_req.bodyReq);
    return _req;
  }

  static DARequestModel get validarToken {
    String uriRoute =
        '/CRUD/PushDispositivos?q=Usuario:{{Usuario}}|Aplicacion:Servicios&c=Token';

    DARequestModel _req = new DARequestModel(
      uriReq: DAController.sessionUrl(uriRoute),
    );

    return _req;
  }
}
