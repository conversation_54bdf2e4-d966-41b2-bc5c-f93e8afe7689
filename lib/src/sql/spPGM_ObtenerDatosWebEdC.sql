/************* spPGM_ObtenerDatosWebEdC ************/
IF EXISTS (SELECT *
FROM sysobjects
WHERE id = object_id('spPGM_ObtenerDatosWebEdC') and type = 'P') DROP PROCEDURE spPGM_ObtenerDatosWebEdC
GO

CREATE PROCEDURE spPGM_ObtenerDatosWebEdC
    @Cliente VARCHAR(10),
    @Empresa VARCHAR(5),
    @Inmueble varchar(90)=NULL,      
    @Local    varchar(90)=NULL,
    @App BIT = 1
AS
IF 1=0 BEGIN  
    SET FMTONLY OFF  
END  
 BEGIN                     
 IF @Inmueble = 'NULL'       
  SET @Inmueble=NULL             
        
 IF @Local    = 'NULL'       
  SET @Local=NULL          
      
  CREATE TABLE #Conceptos (ID int Identity,monto money)    
      
  INSERT INTO #Conceptos (monto)    
  EXEC spEdoCtaCtePangea @Empresa,0,1,@Inmueble,@Local    
      
  INSERT INTO #Conceptos (monto)    
  EXEC spEdoCtaCtePangea @Empresa,0,2,@Inmueble,@Local    
    
  INSERT INTO #Conceptos (monto)    
  EXEC spEdoCtaCtePangea @Empresa,0,3,@Inmueble,@Local    
       
  INSERT INTO #Conceptos (monto)    
  EXEC spEdoCtaCtePangea @Empresa,0,5,@Inmuebl

  INSERT INTO #Conceptos (monto)    
  EXEC spEdoCtaCtePangea @Empresa,0,7,@Inmueble,@Local    
      
  INSERT INTO #Conceptos (monto)    
  EXEC spEdoCtaCtePangea @Empresa,0,6,@Inmueble,@Local    
  
  SELECT TOP 1 
         vc.Local,
         vc.medida,
         vc.descripcion,
         vc.MovID AS CartaOferta,
         vf.Escrituracion AS ValorTerreno
    FROM Cxc JOIN MovTipo ON Cxc.Mov=MovTipo.Mov  AND MovTipo.Modulo IN ('CXC') AND MovTipo.Clave IN ('CXC.F','CXC.C', 'CXC.A','CXC.D','CXC.ANC','CXC.DC','CXC.CD', /*agregado 21/09/2015*/'CXC.AJM')                        
             JOIN vicVWCTOCliente  vc  ON Cxc.ContratoID=vc.ID                       
             JOIN vicVWCTOSFactura vf  ON vc.ID=vf.IDContrato                        
             JOIN vicVWCTOArticulo va  ON vc.ID=va.IDContrato             
   WHERE Cxc.Estatus IN ('PENDIENTE','CONCLUIDO')               
     AND Cxc.Mov NOT IN ('Apartado','Enganche','Mensualidades','Anualidad', '%Pena%', 'Finiquito')                
     AND Cxc.Cliente=@Cliente                
     AND Cxc.Empresa=@Empresa               
     AND vc.Inmueble=ISNULL(@Inmueble,vc.Inmueble) --'ARF3M003E1'                
     AND vc.Local   =ISNULL(@Local,vc.Local)       --'ARF3M003E1-L014'                
     AND (CASE WHEN(SELECT TOP 1 Mov FROM Cxc m  JOIN CxcD n ON n.ID=m.ID   WHERE n.Aplica=Cxc.Mov AND n.AplicaID=Cxc.MovID  AND m.Estatus='CONCLUIDO') = 'Cancelar Documento'  THEN 1  ELSE 0  END) <>1
     AND (@App <> 1 OR (@App = 1 AND Cxc.Mov = 'Cobro Inmobiliario'))
    ORDER BY Cxc.Cliente,Cxc.ContratoID,vc.Inmueble,vc.Local,Cxc.Vencimiento ASC         
  RETURN             
END
GO